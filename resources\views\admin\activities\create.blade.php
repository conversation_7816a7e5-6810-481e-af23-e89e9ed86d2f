@extends('layouts.admin')

@section('title', 'เพิ่มผลงานใหม่ - ผลงานการให้บริการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.activities') }}">ผลงานการให้บริการ</a></li>
<li class="breadcrumb-item active">เพิ่มผลงานใหม่</li>
@endsection

@section('content')
<!-- Header Section - เรียบง่าย -->
<div class="row mb-4">
    <div class="col-12">
        <div class="bg-white rounded-3 p-4 border">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 text-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มผลงานใหม่
                    </h1>
                    <p class="text-muted mb-0">สร้างผลงานการให้บริการพร้อมแกลเลอรี่รูปภาพ</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.activities') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปผลงาน
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.activities.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <!-- Upload Images Section -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-images me-2"></i>อัพโหลดรูปภาพ
                                </h6>
                            </div>
                            <div class="card-body">
                                <label for="images" class="form-label">รูปภาพ <span class="text-danger">*</span></label>
                                <input type="file" class="form-control @error('images') is-invalid @enderror @error('images.*') is-invalid @enderror"
                                       id="images" name="images[]" accept="image/*" multiple required>
                                @error('images')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    สามารถเลือกหลายรูปพร้อมกัน รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                                </div>

                                <!-- Images Preview -->
                                <div id="imagesPreview" class="mt-3 row g-2" style="display: none;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Add More Images to Gallery -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-plus me-2"></i>เพิ่มรูปใหม่ในแกลเลอรี่
                                </h6>
                            </div>
                            <div class="card-body">
                                <label for="additional_images" class="form-label">เลือกรูปภาพเพิ่มเติม</label>
                                <input type="file" class="form-control @error('additional_images') is-invalid @enderror @error('additional_images.*') is-invalid @enderror"
                                       id="additional_images" name="additional_images[]" accept="image/*" multiple>
                                @error('additional_images')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                @error('additional_images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    เพิ่มรูปเข้าไปในแกลเลอรี่ สามารถเลือกหลายรูปพร้อมกัน รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                                </div>

                                <!-- Additional Images Preview -->
                                <div id="additionalImagesPreview" class="mt-3 row g-2" style="display: none;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อเรื่อง <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror"
                               id="title" name="title" value="{{ old('title') }}" required
                               placeholder="เช่น ภาพบรรยากาศงานเปิดตัวผลิตภัณฑ์">
                        @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="3" required
                                  placeholder="เขียนคำอธิบายสั้นๆ เกี่ยวกับผลงานนี้...">{{ old('description') }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control @error('details') is-invalid @enderror"
                                  id="details" name="details" rows="5"
                                  placeholder="เขียนรายละเอียดเพิ่มเติมเกี่ยวกับผลงานนี้...">{{ old('details') }}</textarea>
                        @error('details')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="activity_date" class="form-label">วันที่ <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('activity_date') is-invalid @enderror"
                                       id="activity_date" name="activity_date" value="{{ old('activity_date', now()->format('Y-m-d')) }}" required>
                                @error('activity_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">สถานที่</label>
                                <input type="text" class="form-control @error('location') is-invalid @enderror"
                                       id="location" name="location" value="{{ old('location') }}"
                                       placeholder="เช่น วัดพระแก้ว กรุงเทพฯ">
                                @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0"
                               placeholder="0 = แสดงก่อน, ตัวเลขมาก = แสดงหลัง">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">ใช้สำหรับจัดเรียงลำดับการแสดงในแกลลอรี่</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-eye me-1"></i>เผยแพร่รูปภาพนี้ในแกลลอรี่
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก รูปภาพจะถูกบันทึกแต่ไม่แสดงในหน้าเว็บไซต์</div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกผลงาน
                        </button>
                        <a href="{{ route('admin.activities') }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>ข้อมูลการสร้าง
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>วันที่สร้าง:</strong></td>
                        <td>{{ now()->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>ลำดับเริ่มต้น:</strong></td>
                        <td><span class="badge bg-info">0</span></td>
                    </tr>
                    <tr>
                        <td><strong>สถานะเริ่มต้น:</strong></td>
                        <td>
                            <span class="badge bg-success">
                                <i class="fas fa-eye me-1"></i>เผยแพร่
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb text-warning me-2"></i>คำแนะนำ
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>คำแนะนำการสร้างผลงาน:</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>รูปภาพแรก:</strong> จะถูกตั้งเป็นรูปปกอัตโนมัติ</li>
                        <li><strong>ชื่อเรื่อง:</strong> ใช้ชื่อที่สื่อความหมายชัดเจน</li>
                        <li><strong>คำอธิบาย:</strong> เขียนให้น่าสนใจและครบถ้วน</li>
                        <li><strong>ขนาดไฟล์:</strong> ไม่เกิน 2MB ต่อรูป</li>
                        <li><strong>ไฟล์ที่รองรับ:</strong> JPG, PNG, GIF</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imagesInput = document.getElementById('images');
    const imagesPreview = document.getElementById('imagesPreview');
    const additionalImagesInput = document.getElementById('additional_images');
    const additionalImagesPreview = document.getElementById('additionalImagesPreview');

    imagesInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        imagesPreview.innerHTML = '';

        if (files.length === 0) {
            imagesPreview.style.display = 'none';
            return;
        }

        imagesPreview.style.display = 'block';

        files.forEach((file, index) => {
            // Check file size (2MB = 2 * 1024 * 1024 bytes)
            if (file.size > 2 * 1024 * 1024) {
                alert(`ไฟล์ "${file.name}" มีขนาดใหญ่เกินไป กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB`);
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert(`ไฟล์ "${file.name}" ประเภทไม่ถูกต้อง กรุณาเลือกไฟล์ JPG, PNG หรือ GIF`);
                return;
            }

            // Create preview element
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-3 col-4';

            const cardDiv = document.createElement('div');
            cardDiv.className = 'card';

            const img = document.createElement('img');
            img.className = 'card-img-top';
            img.style.height = '150px';
            img.style.objectFit = 'cover';

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body p-2';

            const fileName = document.createElement('small');
            fileName.className = 'text-muted d-block';
            fileName.textContent = file.name;

            const fileSize = document.createElement('small');
            fileSize.className = 'text-muted';
            fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;

            // Badge for first image
            if (index === 0) {
                const badge = document.createElement('div');
                badge.className = 'position-absolute top-0 end-0 m-2';
                badge.innerHTML = '<span class="badge bg-primary">รูปปก</span>';
                cardDiv.style.position = 'relative';
                cardDiv.appendChild(badge);
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);

            cardBody.appendChild(fileName);
            cardBody.appendChild(fileSize);
            cardDiv.appendChild(img);
            cardDiv.appendChild(cardBody);
            colDiv.appendChild(cardDiv);
            imagesPreview.appendChild(colDiv);
        });
    });

    // Additional images preview functionality
    additionalImagesInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        additionalImagesPreview.innerHTML = '';

        if (files.length === 0) {
            additionalImagesPreview.style.display = 'none';
            return;
        }

        additionalImagesPreview.style.display = 'block';

        files.forEach((file, index) => {
            // Check file size (2MB = 2 * 1024 * 1024 bytes)
            if (file.size > 2 * 1024 * 1024) {
                alert(`ไฟล์ "${file.name}" มีขนาดใหญ่เกินไป กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB`);
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert(`ไฟล์ "${file.name}" ประเภทไม่ถูกต้อง กรุณาเลือกไฟล์ JPG, PNG หรือ GIF`);
                return;
            }

            // Create preview element
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-3 col-4';

            const cardDiv = document.createElement('div');
            cardDiv.className = 'card';

            const img = document.createElement('img');
            img.className = 'card-img-top';
            img.style.height = '120px';
            img.style.objectFit = 'cover';

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body p-2';

            const fileName = document.createElement('small');
            fileName.className = 'text-muted d-block';
            fileName.textContent = file.name;

            const fileSize = document.createElement('small');
            fileSize.className = 'text-muted';
            fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;

            const badge = document.createElement('div');
            badge.className = 'mt-1';
            badge.innerHTML = '<span class="badge bg-success">เพิ่มในแกลเลอรี่</span>';

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);

            cardBody.appendChild(fileName);
            cardBody.appendChild(fileSize);
            cardBody.appendChild(badge);
            cardDiv.appendChild(img);
            cardDiv.appendChild(cardBody);
            colDiv.appendChild(cardDiv);
            additionalImagesPreview.appendChild(colDiv);
        });
    });

    // Auto-generate title from first filename
    imagesInput.addEventListener('change', function(e) {
        const files = e.target.files;
        const titleInput = document.getElementById('title');

        if (files.length > 0 && !titleInput.value) {
            // Remove file extension and format filename
            let filename = files[0].name.replace(/\.[^/.]+$/, "");
            filename = filename.replace(/[-_]/g, ' ');
            filename = filename.charAt(0).toUpperCase() + filename.slice(1);
            titleInput.value = filename;
        }
    });
});
</script>
@endsection
