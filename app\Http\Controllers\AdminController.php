<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\Package;
use App\Models\Contact;
use App\Models\Activity;
use App\Models\ActivityImage;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\Storage;

class AdminController extends Controller
{
    public function dashboard()
    {
        $stats = [
            'services_count' => Service::count(),
            'packages_count' => Package::count(),
            'activities_count' => Activity::count(),
            'contacts_count' => Contact::count(),
            'unread_contacts' => Contact::unread()->count(),
        ];

        $recent_contacts = Contact::latest()->take(5)->get();

        // Share unread contacts count with all views
        view()->share('unread_contacts', $stats['unread_contacts']);

        return view('admin.dashboard', compact('stats', 'recent_contacts'));
    }

    // Services Management
    public function services()
    {
        $services = Service::ordered()->paginate(10); // 10 items per page
        return view('admin.services.index', compact('services'));
    }

    public function createService()
    {
        return view('admin.services.create');
    }

    public function storeService(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('services', 'public');
        }

        Service::create($data);

        return redirect()->route('admin.services')->with('success', 'เพิ่มบริการเรียบร้อยแล้ว');
    }

    public function editService($id)
    {
        $service = Service::findOrFail($id);
        return view('admin.services.edit', compact('service'));
    }

    public function updateService(Request $request, $id)
    {
        $service = Service::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            // Delete old image
            if ($service->image) {
                Storage::disk('public')->delete($service->image);
            }
            $data['image'] = $request->file('image')->store('services', 'public');
        }

        $service->update($data);

        return redirect()->route('admin.services')->with('success', 'แก้ไขบริการเรียบร้อยแล้ว');
    }

    public function deleteService($id)
    {
        $service = Service::findOrFail($id);

        // Delete image
        if ($service->image) {
            Storage::disk('public')->delete($service->image);
        }

        $service->delete();

        return redirect()->route('admin.services')->with('success', 'ลบบริการเรียบร้อยแล้ว');
    }

    // Packages Management
    public function packages()
    {
        $packages = Package::ordered()->paginate(8); // 8 items per page
        return view('admin.packages.index', compact('packages'));
    }

    public function createPackage()
    {
        return view('admin.packages.create');
    }

    public function storePackage(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'features' => 'required|string',
            'duration' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('packages', 'public');
        }

        Package::create($data);

        return redirect()->route('admin.packages')->with('success', 'เพิ่มแพคเกจเรียบร้อยแล้ว');
    }

    public function editPackage($id)
    {
        $package = Package::findOrFail($id);
        return view('admin.packages.edit', compact('package'));
    }

    public function updatePackage(Request $request, $id)
    {
        $package = Package::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'features' => 'required|string',
            'duration' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            // Delete old image
            if ($package->image) {
                Storage::disk('public')->delete($package->image);
            }
            $data['image'] = $request->file('image')->store('packages', 'public');
        }

        $package->update($data);

        return redirect()->route('admin.packages')->with('success', 'แก้ไขแพคเกจเรียบร้อยแล้ว');
    }

    public function deletePackage($id)
    {
        $package = Package::findOrFail($id);

        // Delete image
        if ($package->image) {
            Storage::disk('public')->delete($package->image);
        }

        $package->delete();

        return redirect()->route('admin.packages')->with('success', 'ลบแพคเกจเรียบร้อยแล้ว');
    }

    // Activities Management
    public function activities()
    {
        $activities = Activity::with('images')->ordered()->paginate(12); // 12 items per page
        return view('admin.activities.index', compact('activities'));
    }

    public function createActivity()
    {
        return view('admin.activities.create');
    }

    public function storeActivity(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'activity_date' => 'required|date',
            'location' => 'nullable|string|max:255',
            'images' => 'required|array|min:1',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_images' => 'nullable|array',
            'additional_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        $data = $request->only(['title', 'description', 'details', 'activity_date', 'location', 'is_active', 'sort_order']);

        // Set default image for backward compatibility
        $data['image'] = 'activities/default.jpg';

        $activity = Activity::create($data);

        $currentOrder = 0;

        // Handle main images
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $imagePath = $image->store('activities', 'public');
                $currentOrder++;

                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $imagePath,
                    'caption' => $request->input('title') . ' - รูปที่ ' . $currentOrder,
                    'is_cover' => $index === 0, // First image is cover
                    'sort_order' => $currentOrder
                ]);
            }
        }

        // Handle additional images for gallery
        if ($request->hasFile('additional_images')) {
            foreach ($request->file('additional_images') as $index => $image) {
                $imagePath = $image->store('activities', 'public');
                $currentOrder++;

                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $imagePath,
                    'caption' => $request->input('title') . ' - รูปที่ ' . $currentOrder,
                    'is_cover' => false, // Additional images are never cover
                    'sort_order' => $currentOrder
                ]);
            }
        }

        $message = 'เพิ่มผลงานเรียบร้อยแล้ว';

        if ($request->hasFile('additional_images')) {
            $additionalCount = count($request->file('additional_images'));
            $message .= ' (พร้อมรูปเพิ่มเติม ' . $additionalCount . ' รูป)';
        }

        return redirect()->route('admin.activities')->with('success', $message);
    }

    public function editActivity($id)
    {
        $activity = Activity::findOrFail($id);
        return view('admin.activities.edit', compact('activity'));
    }

    public function updateActivity(Request $request, $id)
    {
        $activity = Activity::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'activity_date' => 'required|date',
            'location' => 'nullable|string|max:255',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'new_images' => 'nullable|array',
            'new_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        $data = $request->only(['title', 'description', 'details', 'activity_date', 'location', 'is_active', 'sort_order']);

        $activity->update($data);

        // Handle cover image change
        if ($request->hasFile('cover_image')) {
            // Find current cover image
            $currentCover = $activity->images()->where('is_cover', true)->first();

            if ($currentCover) {
                // Delete old cover image file
                Storage::disk('public')->delete($currentCover->image_path);

                // Update with new cover image
                $coverImagePath = $request->file('cover_image')->store('activities', 'public');
                $currentCover->update([
                    'image_path' => $coverImagePath,
                    'caption' => $request->input('title') . ' - รูปปก'
                ]);
            } else {
                // Create new cover image if no cover exists
                $coverImagePath = $request->file('cover_image')->store('activities', 'public');
                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $coverImagePath,
                    'caption' => $request->input('title') . ' - รูปปก',
                    'is_cover' => true,
                    'sort_order' => 0
                ]);
            }
        }

        // Handle new images for gallery
        if ($request->hasFile('new_images')) {
            $currentMaxOrder = $activity->images()->max('sort_order') ?? 0;

            foreach ($request->file('new_images') as $index => $image) {
                $imagePath = $image->store('activities', 'public');

                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $imagePath,
                    'caption' => $request->input('title') . ' - รูปที่ ' . ($currentMaxOrder + $index + 1),
                    'is_cover' => false, // Gallery images are never cover
                    'sort_order' => $currentMaxOrder + $index + 1
                ]);
            }
        }

        $message = 'แก้ไขผลงานเรียบร้อยแล้ว';

        if ($request->hasFile('cover_image') && $request->hasFile('new_images')) {
            $message .= ' (เปลี่ยนรูปปกและเพิ่มรูปในแกลเลอรี่แล้ว)';
        } elseif ($request->hasFile('cover_image')) {
            $message .= ' (เปลี่ยนรูปปกแล้ว)';
        } elseif ($request->hasFile('new_images')) {
            $message .= ' (เพิ่มรูปในแกลเลอรี่แล้ว)';
        }

        return redirect()->route('admin.activities')->with('success', $message);
    }

    public function deleteActivity($id)
    {
        $activity = Activity::findOrFail($id);

        // Delete all image files
        foreach ($activity->images as $image) {
            Storage::disk('public')->delete($image->image_path);
        }

        // Delete old image file (backward compatibility)
        if ($activity->image) {
            Storage::disk('public')->delete($activity->image);
        }

        $activity->delete();

        return redirect()->route('admin.activities')->with('success', 'ลบผลงานเรียบร้อยแล้ว');
    }

    public function setCoverImage($id)
    {
        $image = ActivityImage::findOrFail($id);
        $activity = $image->activity;

        // Remove cover from all images of this activity
        ActivityImage::where('activity_id', $activity->id)->update(['is_cover' => false]);

        // Set this image as cover
        $image->update(['is_cover' => true]);

        return response()->json(['success' => true, 'message' => 'ตั้งรูปปกเรียบร้อยแล้ว']);
    }

    public function deleteImage($id)
    {
        $image = ActivityImage::findOrFail($id);
        $activity = $image->activity;

        // Check if this is the only image
        if ($activity->images()->count() <= 1) {
            return response()->json(['success' => false, 'message' => 'ไม่สามารถลบรูปได้ เนื่องจากต้องมีรูปอย่างน้อย 1 รูป']);
        }

        // If this is cover image, set another image as cover
        if ($image->is_cover) {
            $nextImage = $activity->images()->where('id', '!=', $id)->first();
            if ($nextImage) {
                $nextImage->update(['is_cover' => true]);
            }
        }

        // Delete image file
        Storage::disk('public')->delete($image->image_path);

        // Delete image record
        $image->delete();

        return response()->json(['success' => true, 'message' => 'ลบรูปเรียบร้อยแล้ว']);
    }

    // Contacts Management
    public function contacts()
    {
        $contacts = Contact::latest()->get();
        return view('admin.contacts.index', compact('contacts'));
    }

    public function showContact($id)
    {
        $contact = Contact::findOrFail($id);

        // Mark as read
        if (!$contact->is_read) {
            $contact->update(['is_read' => true]);
        }

        return view('admin.contacts.show', compact('contact'));
    }

    public function deleteContact($id)
    {
        $contact = Contact::findOrFail($id);
        $contact->delete();

        return redirect()->route('admin.contacts')->with('success', 'ลบข้อความเรียบร้อยแล้ว');
    }

    // Site Settings
    public function settings()
    {
        $settings = [
            'site_name' => SiteSetting::get('site_name', ''),
            'site_description' => SiteSetting::get('site_description', ''),
            'contact_phone' => SiteSetting::get('contact_phone', ''),
            'contact_email' => SiteSetting::get('contact_email', ''),
            'contact_address' => SiteSetting::get('contact_address', ''),
            'facebook_url' => SiteSetting::get('facebook_url', ''),
            'line_id' => SiteSetting::get('line_id', ''),
        ];

        return view('admin.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string',
            'contact_phone' => 'required|string|max:20',
            'contact_email' => 'required|email|max:255',
            'contact_address' => 'required|string',
            'facebook_url' => 'nullable|url',
            'line_id' => 'nullable|string|max:255',
        ]);

        foreach ($request->all() as $key => $value) {
            if ($key !== '_token' && $key !== '_method') {
                SiteSetting::set($key, $value);
            }
        }

        return redirect()->route('admin.settings')->with('success', 'บันทึกการตั้งค่าเรียบร้อยแล้ว');
    }
}
